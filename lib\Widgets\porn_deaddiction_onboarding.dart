import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class PornDeaddictionOnboarding extends StatefulWidget {
  const PornDeaddictionOnboarding({super.key});

  @override
  State<PornDeaddictionOnboarding> createState() =>
      _PornDeaddictionOnboardingState();
}

class _PornDeaddictionOnboardingState extends State<PornDeaddictionOnboarding> {
  final List<String> sentences = [
    "You are much stronger than you think , all you have to do is to believe in yourself.",
    "You are not alone in this war , I will be your shield",
    "Lets start with knowing the stage of your addiction"
  ];

  int currentSentenceIndex = 0;
  List<String> words = [];
  List<bool> wordVisible = [];
  bool sentenceVisible = true;

  @override
  void initState() {
    super.initState();
    _startSentenceAnimation();
  }

  Future<void> _startSentenceAnimation() async {
    while (currentSentenceIndex < sentences.length) {
      words = sentences[currentSentenceIndex].split(' ');
      wordVisible = List.generate(words.length, (_) => false);
      sentenceVisible = true;
      setState(() {});

      // Show words one by one
      for (int i = 0; i < words.length; i++) {
        await Future.delayed(Duration(milliseconds: 300));
        wordVisible[i] = true;
        setState(() {});
      }

      // Wait before fade out
      await Future.delayed(Duration(seconds: 2));
      sentenceVisible = false;
      setState(() {});

      await Future.delayed(Duration(milliseconds: 500));
      currentSentenceIndex++;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: AnimatedSwitcher(
          duration: Duration(milliseconds: 500),
          child: currentSentenceIndex < sentences.length
              ? sentenceVisible
                  ? Wrap(
                      key: ValueKey(currentSentenceIndex),
                      spacing: 6,
                      alignment: WrapAlignment.center,
                      children: List.generate(words.length, (index) {
                        return AnimatedOpacity(
                          duration: Duration(milliseconds: 300),
                          opacity: wordVisible[index] ? 1.0 : 0.0,
                          child: Text(
                            words[index],
                            style: GoogleFonts.notoSerif(
                                color: Colors.blue.shade200,
                                fontSize: 20,
                                fontWeight: FontWeight.bold),
                          ),
                        );
                      }),
                    )
                  : SizedBox.shrink()
              : Text(
                  "Let's begin!",
                  style: GoogleFonts.orbitron(
                    textStyle: TextStyle(
                      fontSize: 28,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildQuestionaire() {

    TextEditingController _daysController = TextEditingController();
    return Center(
        child: Column(children: [
      Text("How many days do you masterbate in a week ?"),
      SizedBox(height: 30),
      TextFormField(
        controller: _daysController,
        decoration: const InputDecoration(
          border: UnderlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly,
        ],
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter a number';
          }
          final number = int.tryParse(value);
          if (number == null) {
            return 'Please enter a valid number';
          }
          if (number < 1 || number > 6) {
            return 'Please enter a number between 1 and 6';
          }
          return null; // Return null if the input is valid
        },
      ),
      SizedBox(height: 30),
      ElevatedButton(
        onPressed: () {
          _
        },
        child: Text('Submit'),
      )
    ]));
  }
}

import 'package:v18ui/Services/Service.dart';

class PornDeaddictionService {
  final Map<String, MonthData> pornDeaddictionData;

  PornDeaddictionService({required this.pornDeaddictionData});

  // Factory constructor to create a PornDeaddictionService object from JSON
  factory PornDeaddictionService.fromJson(Map<String, dynamic> json) {
    final Map<String, MonthData> parsedData = {};
    if (json['pornDeaddictionService'] != null) {
      json['pornDeaddictionService'].forEach((key, value) {
        parsedData[key] = MonthData.fromJson(value);
      });
    }
    return PornDeaddictionService(pornDeaddictionData: parsedData);
  }

  // Convert PornDeaddictionService object to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    json['pornDeaddictionService'] = pornDeaddictionData.map((key, value) {
      return MapEntry(key, value.toJson());
    });
    return json;
  }

  // Insert a new record for a specific month
  void insertRecord(String month, int stage, List<int> pornWatchedDates) {
    if (!pornDeaddictionData.containsKey(month)) {
      pornDeaddictionData[month] =
          MonthData(stage: stage, pornWatchedDates: pornWatchedDates);
    } else {
      throw Exception(
          "Record for $month already exists. Use modifyRecord instead.");
    }
  }

  // Modify an existing record for a specific month
  void modifyRecord(String month, int stage, List<int> pornWatchedDates) {
    if (pornDeaddictionData.containsKey(month)) {
      pornDeaddictionData[month] =
          MonthData(stage: stage, pornWatchedDates: pornWatchedDates);
    } else {
      throw Exception(
          "Record for $month does not exist. Use insertRecord instead.");
    }
  }

  // Get data for a specific month
  MonthData? getRecordForMonth(String month) {
    return pornDeaddictionData[month];
  }

  // Get watched dates for a specific month
  List<int> getWatchedDatesForMonth(String month) {
    return pornDeaddictionData[month]?.pornWatchedDates ?? [];
  }

  // Get stage for a specific month
  int getStageForMonth(String month) {
    return pornDeaddictionData[month]?.stage ?? 0;
  }

  // Update the service
  Future<bool> updateService() async {
    return await Service().updateService('pornDeaddictionService', toJson());
  }

  // Delete the service
  Future<bool> deleteService() async {
    return await Service().deleteService('pornDeaddictionService');
  }

  /** Following functions only returns how many days are allowed in a month
   * as per stage */

  static List<DateTime> getStageDates(int year, int month, int stage) {
    List<DateTime> dates = [];
    DateTime currentDate = DateTime(year, month, 1);

    switch (stage) {
      case 1: // One day in a month
        dates.add(currentDate);
        break;

      case 2: // One day in two weeks
        while (currentDate.month == month) {
          dates.add(currentDate);
          currentDate = currentDate.add(Duration(days: 14));
        }
        break;

      case 3: // One day in a week
        while (currentDate.month == month) {
          dates.add(currentDate);
          currentDate = currentDate.add(Duration(days: 7));
        }
        break;

      case 4: // Two days in a week
        while (currentDate.month == month) {
          dates.add(currentDate);
          if (currentDate.add(Duration(days: 3)).month == month) {
            dates.add(currentDate.add(Duration(days: 3)));
          }
          currentDate = currentDate.add(Duration(days: 7));
        }
        break;

      case 5: // Five days in a week
        while (currentDate.month == month) {
          for (int i = 0; i < 5; i++) {
            if (currentDate.month == month) {
              dates.add(currentDate);
              currentDate = currentDate.add(Duration(days: 1));
            }
          }
          currentDate = currentDate.add(Duration(days: 2)); // Skip weekends
        }
        break;

      default:
        throw ArgumentError('Invalid stage number');
    }

    return dates;
  }
}

class MonthData {
  final int stage;
  final List<int> pornWatchedDates;

  MonthData({required this.stage, required this.pornWatchedDates});

  // Factory constructor to create a MonthData object from JSON
  factory MonthData.fromJson(Map<String, dynamic> json) {
    return MonthData(
      stage: json['stage'],
      pornWatchedDates: List<int>.from(json['porn_watched_dates']),
    );
  }

  // Convert MonthData object to JSON
  Map<String, dynamic> toJson() {
    return {
      'stage': stage,
      'porn_watched_dates': pornWatchedDates,
    };
  }
}

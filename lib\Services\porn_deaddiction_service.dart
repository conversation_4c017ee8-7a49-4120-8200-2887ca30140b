import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Screens/brahmacharyamode_screen.dart';
import 'package:v18ui/Services/Service.dart';
import 'package:v18ui/Widgets/brahmacharya_dialog.dart';

class PornDeaddictionService {
  Map<String, MonthData>? CalendarData;
  bool? serviceActive;
  final bool userGenerated = false;

  PornDeaddictionService? get pornDeaddictionServiceData =>
      ConfigManager().services?.pornDeaddictionService;

  Map<String, MonthData>? get calendarData =>
      pornDeaddictionServiceData?.CalendarData;

  set pornDeaddictionServiceData(PornDeaddictionService? value) {
    ConfigManager().services!.pornDeaddictionService = value;
  }

  PornDeaddictionService({this.CalendarData, this.serviceActive});

  // Factory constructor to create a PornDeaddictionService object from JSON
  factory PornDeaddictionService.fromJson(Map<String, dynamic> json) {
    var serviceActive = json['serviceActive'] ?? false;
    Map<String, MonthData> CalendarData = {};
    if (json['CalendarData'] != null) {
      json['CalendarData'].forEach((key, value) {
        CalendarData[key] = MonthData.fromJson(value as Map<String, dynamic>);
      });
    }
    return PornDeaddictionService(
        CalendarData: CalendarData, serviceActive: serviceActive);
  }

  // Convert PornDeaddictionService object to JSON
  Map<String, dynamic> toJson() {
    return {
      'serviceActive': pornDeaddictionServiceData?.serviceActive ?? false,
      'CalendarData': pornDeaddictionServiceData?.CalendarData ?? [],
    };
  }

  Future<bool?> confirmActivation(context) async {
    return showDialog<bool>(
        context: context,
        builder: (context) {
          return const BrahmacharyaModeDialog();
        });
  }

  activateService(context) async {
    /**Step 1 : Show Brahmacharya dialog box */

    /** We will give two options in dialog - "Move ahead" , "Cancel". If user clicks on Move ahead , then
     * we will show additional questionaire screen asking about the stage user wants to start from. After that we will move ahead
     * with the service activation. 
      */

    bool? response = await confirmActivation(context);
    if (response!) {
      /**Step 2 : Show Questionaire screen */
      //we are skipping this step for now.
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => const BrahmacharyaModeScreen()));
    }

    pornDeaddictionServiceData!.serviceActive = true;
    updateService();
  }

  // Insert a new record when service is activated for the first time in a month
  void insertRecord(int stage) {
    final key = getCurrentMonth();
    calendarData!.addAll({
      key: MonthData(
          stage: stage,
          pornWatchedDates: [],
          watchedCount: 0,
          allowedCount:
              getStageDates(DateTime.now().year, DateTime.now().month, stage)
                  .length)
    });
    updateService();
  }

  // Modify an existing record for a specific month
  void modifyRecord(String month, DateTime pornWatchedDate) {
    //adding new date
    calendarData![month]!.pornWatchedDates.add(pornWatchedDate);
    //increasing watched count
    calendarData![month]!.watchedCount++;
    updateService();
  }

  static String getCurrentMonth() {
    final now = DateTime.now();
    final month = now.month.toString().padLeft(2, '0'); // ensures 2-digit month
    return '${now.year}-$month';
  }

  // Get watched data for a specific month
  MonthData getWatchedDates(String month) {
    return pornDeaddictionServiceData!.calendarData![month] ??
        MonthData(
            stage: 0, pornWatchedDates: [], watchedCount: 0, allowedCount: 0);
  }

  // Update the service
  Future<bool> updateService() async {
    // returning value and updating variable
    return await Service()
        .updateService('pornDeaddictionService', toJson())
        .then((value) {
      PornDeaddictionService().pornDeaddictionServiceData =
          ConfigManager().services!.pornDeaddictionService;
      PornDeaddictionService().CalendarData =
          ConfigManager().services!.pornDeaddictionService!.CalendarData;
      return value;
    });
  }

  // Deactivate the service
  Future<bool> DeactivateService() async {
    pornDeaddictionServiceData!.serviceActive = false;
    await updateService();
    return true;
  }

  /** Following functions only returns how many days are allowed in a month as per stage */

  List<DateTime> getStageDates(int year, int month, int stage) {
    List<DateTime> dates = [];
    DateTime FirstDate = DateTime(year, month, 1);

    switch (stage) {
      case 1: // One day in a month
        dates.add(FirstDate);
        break;

      case 2: // One day in two weeks
        while (FirstDate.month == month) {
          dates.add(FirstDate);
          FirstDate = FirstDate.add(Duration(days: 14));
        }
        break;

      case 3: // One day in a week
        while (FirstDate.month == month) {
          dates.add(FirstDate);
          FirstDate = FirstDate.add(Duration(days: 7));
        }
        break;

      case 4: // Two days in a week
        while (FirstDate.month == month) {
          dates.add(FirstDate);
          if (FirstDate.add(Duration(days: 3)).month == month) {
            dates.add(FirstDate.add(Duration(days: 3)));
          }
          FirstDate = FirstDate.add(Duration(days: 3));
        }
        break;

      case 5: // Five days in a week
        while (FirstDate.month == month) {
          dates.add(FirstDate);
          if (FirstDate.add(Duration(days: 3)).month == month) {
            dates.add(FirstDate.add(Duration(days: 2)));
          }
          FirstDate = FirstDate.add(Duration(days: 2));
        }
        break;
      default:
        dates = [];
        break;
    }

    return dates;
  }
}

class MonthData {
  final int stage;
  List<DateTime> pornWatchedDates;
  int watchedCount;
  int allowedCount;

  MonthData(
      {required this.stage,
      required this.pornWatchedDates,
      required this.watchedCount,
      required this.allowedCount});

  // Factory constructor to create a MonthData object from JSON
  factory MonthData.fromJson(Map<String, dynamic> json) {
    return MonthData(
        stage: json['stage'],
        pornWatchedDates: ((json['porn_watched_dates']) as List<dynamic>)
            .map((dateStr) => DateTime.parse(dateStr))
            .toList(),
        watchedCount: json['watchedCount'],
        allowedCount: json['allowedCount']);
  }

  // Convert MonthData object to JSON
  Map<String, dynamic> toJson() {
    return {
      'stage': stage,
      'porn_watched_dates': pornWatchedDates,
      'watchedCount': watchedCount,
      'allowedCount': allowedCount
    };
  }
}

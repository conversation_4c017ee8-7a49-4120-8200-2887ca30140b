{"System_categorised": {"Payments": ["com.google.android.apps.nbu.paisa.user", "com.phonepe.app", "net.one97.paytm", "com.paytmmall", "in.org.npci.upiapp", "in.amazon.mShop.android.shopping", "com.csam.icici.bank.imobile", "com.sbi.upi", "com.myairtelapp", "com.icicibank.pockets", "com.snapwork.hdfc", "com.enstage.wibmo.hdfc", "com.mobikwik_new", "com.upi.axispay", "com.axis.mobile", "com.freecharge.android", "com.samsung.android.spaymini", "com.samsung.android.spay", "com.dreamplug.androidapp", "com.bankofbaroda.upi", "com.fss.pnbpsp", "com.fss.pnbpsp", "com.fss.unbipsp", "com.mycompany.kvb", "com.fss.vijayapsp", "com.dena.upi.gui", "com.fss.jnkpsp", "com.olive.kotak.upi", "com.bsb.hike", "com.fss.idfcpsp", "com.YesBank", "com.abipbl.upi", "com.microsoft.mobile.polymer", "com.finopaytech.bpayfino", "com.mgs.obcbank", "com.upi.federalbank.org.lotza", "com.mgs.induspsp", "ai.wizely.android", "com.olive.dcb.upi", "com.mgs.yesmerchantnative.prod", "com.dbs.in.digitalbank", "com.rblbank.mobank", "in.chillr", "com.citrus.citruspay", "com.SIBMobile", "com.mipay.wallet.in", "com.msf.angelmobile", "com.fundsindia", "com.muthootfinance.imuthoot", "com.angelbroking.angelwealth", "org.altruist.BajajExperia", "in.bajajfinservmarkets.app", "in.indwealth"], "Shopping": ["in.amazon.mShop.android.shopping", "com.flipkart.android", "com.meesho.supply", "com.flipkart.shopsy", "com.zepto.android", "com.grofers.customerapp", "in.swiggy.android", "com.dominos", "com.bigbasket.mobileapp", "com.dunzo.user", "com.jiomart", "com.snapdeal.main", "com.shopclues", "com.nykaa.android", "com.myntra.android", "com.firstcry.buyer", "com.urbanclap.urbanclap", "com.olacabs.customer", "com.uber.client"], "Gaming": ["com.ludo.king", "com.dts.freefiremax", "com.miniclip.carrom", "com.miniclip.cricketleague", "com.zupee.cash", "com.indianbikesdriving3d.imbd3d", "com.pubg.imobile", "com.king.candycrushsaga", "com.supercell.clashofclans", "com.supercell.clashroyale", "com.roblox.client", "com.mojang.minecraftpe", "com.gameloft.android.ANMP.GloftA9HM", "com.ea.gp.fifamobile", "com.kiloo.subwaysurf", "com.nianticlabs.pokemongo", "com.king.farmheroessaga", "com.king.bubblewitch3", "com.king.petrescuesaga", "com.king.candycrushsodasaga"], "Entertainment": ["com.google.android.youtube", "in.startv.hotstar", "com.mxtech.videoplayer.ad", "com.bt.bms", "com.kukufm", "com.kukutv.app", "com.netflix.mediaclient", "com.amazon.avod.thirdpartyclient", "com.jio.media.jiobeats", "com.spotify.music", "com.gaana", "com.jio.media.jiobeats", "com.audible.application", "com.sonyliv", "com.zee5.android", "com.hungama.myplay.activity", "com.wynk.music", "com.saavn.android", "com.tiktok.android", "com.voot", "com.instagram.android", "com.facebook.katana", "com.snapchat.android", "com.instagram.threads", "com.facebook.orca", "com.twitter.android", "com.bumble.app", "com.google.android.youtube", "com.chingari.app", "com.roposo.roposo", "com.mojapp.android", "com.josh.android", "com.flik.android", "com.helou.android ", "com.sharechat.android"], "Social": ["org.telegram.messenger", "com.whatsapp", "com.discord", "com.signal.android"], "Utilities": ["com.truecaller", "com.whereismytrain.android", "com.mobond.mindicator", "com.jio.myjio", "in.uidai.rdservice", "com.google.android.apps.nbu.files", "com.google.android.apps.translate", "com.google.android.apps.docs", "com.google.android.apps.maps", "com.google.android.apps.photos", "com.google.android.apps.calendar", "com.google.android.apps.messaging", "com.google.android.apps.contacts", "com.google.android.apps.keep", "com.google.android.apps.tachyon", "com.google.android.apps.meetings", "com.google.android.apps.authenticator2", "com.google.android.apps.accessibility.soundamplifier", "com.google.android.apps.accessibility.voiceaccess", "com.google.android.apps.accessibility.magnifier"], "Productivity": ["com.microsoft.office.word", "com.microsoft.office.excel", "com.microsoft.office.powerpoint", "com.microsoft.office.outlook", "com.google.android.keep", "com.google.android.apps.docs", "com.google.android.apps.calendar", "com.google.android.apps.tasks", "com.google.android.apps.meetings", "com.google.android.apps.authenticator2", "com.google.android.apps.accessibility.soundamplifier", "com.google.android.apps.accessibility.voiceaccess", "com.google.android.apps.accessibility.magnifier", "com.google.android.apps.translate", "com.google.android.apps.tachyon", "com.google.android.apps.messaging", "com.google.android.apps.contacts", "com.google.android.apps.photos"]}, "UserCategorised": {}, "Uncategorised": []}
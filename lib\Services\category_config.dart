import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:v18ui/DataObjects/app_data.dart';
import 'package:v18ui/Services/category_settings.dart';
import 'package:v18ui/Services/gpt_service.dart';

class CategoryConfig {
  Future<void> defaultCategorySettings() async {
    final String commonCategorisedAppsString = await rootBundle
        .loadString('assets/files/common_categorised_apps.json');
    final commonCategorydata = jsonDecode(commonCategorisedAppsString);
    categorizeApps(AppData.all_apps.map((e) => e.package_name).toList(),
        commonCategorydata);
  }

  Future<Map<String, CategorySettings>> categorizeApps(
    List<String> installedPackages,
    Map<String, List<String>> predefinedCategories,
  ) async {
    final Map<String, CategorySettings> result = {};
    final List<String> uncategorized = [];

    // Step 1: Build reverse lookup: package → category name
    final Map<String, String> packageToCategory = {};
    for (var entry in predefinedCategories.entries) {
      for (var pkg in entry.value) {
        packageToCategory[pkg] = entry.key;
      }
    }

    // Step 2: Categorize in a single pass
    for (var pkg in installedPackages) {
      final category = packageToCategory[pkg];
      if (category != null) {
        result.putIfAbsent(
            category,
            () => CategorySettings(
                  dailyAllowedTime: "",
                  package: [],
                  timeoutOver: false,
                  todayTotalWatchTime: "",
                  categoryName: category,
                  categoryType: "System_generated",
                  weeklyOffAllowed: false,
                ));
        result[category]!.package.add(pkg);
      } else {
        uncategorized.add(pkg);
      }
    }

    // Step 3: Ask GPT to classify uncategorized packages
    if (uncategorized.isNotEmpty) {
      final gptResults = await findCategory(uncategorized);

      for (var entry in gptResults.entries) {
        result.putIfAbsent(
            entry.key,
            () => CategorySettings(
                  dailyAllowedTime: "",
                  package: [],
                  timeoutOver: false,
                  todayTotalWatchTime: "",
                  categoryName: entry.key,
                  categoryType: "System_generated",
                  weeklyOffAllowed: false,
                ));
        result[entry.key]!.package.addAll(entry.value);
      }
    }

    return result;
  }

  Future<Map<String, List<String>>> findCategory(
      List<String> unCategorisedApps) async {
    final gpt = GPTService(
        // remove it from here
        apiKey:
            "********************************************************************************************************************************************************************");

    final result = await gpt.categorizePackages(unCategorisedApps);

    print(result);
    return result;
  }

  addCategory(String packageName) {}

  deleteCategory(String packageName) {}
}

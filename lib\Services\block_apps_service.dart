import 'package:v18ui/DataObjects/blocked_apps.dart';
import 'package:v18ui/Services/Service.dart';

class BlockAppsService {
  List<BlockedApp>? appsBlocked;

  BlockAppsService({this.appsBlocked});

  factory BlockAppsService.fromJson(List<dynamic> json) {
    return BlockAppsService(
      // When json is created for the first time , it will be null , hence included below condition
      appsBlocked: json != null
          ? json.map((app) => BlockedApp.fromJson(app)).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() => {
        'blockedApps': appsBlocked?.map((x) => x.toJson()).toList() ?? [],
      };

  Future<bool> updateService() async {
    return await Service()
        .updateService('blockedApps', toJson()['blockedApps']);
  }

  Future<bool> deleteService() async {
    return await Service().deleteService('blockedApps');
  }
}

import 'package:flutter/material.dart';
import 'package:v18ui/Screens/focused_youtube_screen.dart';
import 'package:v18ui/Screens/safe_app_list.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';
import 'dart:typed_data';
import 'package:v18ui/Screens/app_list_screen.dart';
import 'package:v18ui/DataObjects/app_data.dart';
import 'package:v18ui/services/auth_service.dart';
import 'package:v18ui/Screens/sign_in_screen.dart';
import 'package:table_calendar/table_calendar.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  final List<DateTime> _markedDates = PornDeaddictionService()
      .getStageDates(DateTime.now().month, DateTime.now().year, 5)
      .map((date) => DateTime(date.year, date.month, date.day)) // Normalize
      .toList();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "V18",
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.redAccent,
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            _buildDrawerHeader(),
            _buildDrawerItem(Icons.check_circle, "Safe App List", () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => SafeAppList()),
              );
            }),
            _buildDrawerItem(Icons.support, "Contact Support", () {}),
            _buildDrawerItem(Icons.star, "Rate Us", () {}),
            _buildDrawerItem(Icons.share, "Refer a Friend", () {}),
            _buildDrawerItem(Icons.card_membership, "Your Plans", () {}),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          vertical: MediaQuery.of(context).size.height * 0.02,
          horizontal: MediaQuery.of(context).size.width * 0.05,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10),
            _buildCalendar(),
            SizedBox(height: 10),
            largeTile("Block App", Icons.block, Colors.red, () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AppList()),
              );
            }),
            SizedBox(height: 10),
            Text(
              "Blocked Apps:",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Container(
              height: MediaQuery.of(context).size.height * 0.4, // Adjust height
              decoration: BoxDecoration(
                color: Colors.grey[200], // Light background color
                borderRadius: BorderRadius.circular(10), // Rounded corners
                border: Border.all(color: Colors.grey, width: 1), // Border
              ),
              padding: EdgeInsets.all(10), // Padding inside the container
              child: FutureBuilder(
                future: AppData.get_blocked_app_info(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(child: Text('Error: ${snapshot.error}'));
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return Center(child: Text('No blocked apps found.'));
                  } else {
                    var data = snapshot.data!;
                    return ListView.builder(
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        return blockedAppTile(
                          data[index].app_label,
                          data[index].icon ?? Uint8List.fromList([]),
                          Colors.blue,
                          data[index].blocking_start_time.toString(),
                          data[index].blocking_end_time.toString(),
                        );
                      },
                    );
                  }
                },
              ),
            ),
            SizedBox(height: 10),
            largeTile(
                "Focused YouTube", Icons.ondemand_video, Colors.deepPurple, () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const FocusedYouTubeApp()),
              );
            }),
            SizedBox(height: 10),
            largeTile(
                "Porn Block", Icons.no_adult_content, Colors.orange, () {}),
            SizedBox(height: 10),
            largeTile(
                "Block Sites / Words", Icons.language, Colors.blue, () {}),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendar() {
    return TableCalendar(
      firstDay: DateTime.utc(2023, 1, 1),
      lastDay: DateTime.utc(2030, 12, 31),
      focusedDay: _focusedDay,
      selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
      calendarFormat: _calendarFormat,
      onDaySelected: (selectedDay, focusedDay) {
        setState(() {
          _selectedDay = selectedDay;
          _focusedDay = focusedDay;
        });
      },
      availableCalendarFormats: {
        // 👈 Restrict to only Month format
        CalendarFormat.month: 'Month',
      },
      calendarBuilders: CalendarBuilders(
        defaultBuilder: (context, day, focusedDay) {
          if (_markedDates.any((marked) => isSameDay(marked, day))) {
            return Container(
              margin: EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: Colors.green, // Change marker color
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: Text(
                '${day.day}',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }
          return null;
        },
      ),
    );
  }

  /// 🔥 Blocked Apps Tile
  Widget blockedAppTile(String appName, Uint8List icon, Color color,
      String start_time, String end_time) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        leading: Image.memory(
          icon,
          width: 40,
          height: 40,
        ),
        title: Text(
          appName,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        subtitle: Text("Blocked from $start_time till $end_time"),
        trailing: Icon(Icons.lock, color: Colors.red),
        onTap: () {}, // Add custom action if needed
      ),
    );
  }

  /// 🔥 Large Tile with Tap Action
  Widget largeTile(String title, IconData icon, Color color, Function onTap) {
    return GestureDetector(
      onTap: () => onTap(),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: MediaQuery.of(context).size.height * 0.02,
          horizontal: MediaQuery.of(context).size.width * 0.05,
        ),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 32),
                SizedBox(width: 15),
                Text(
                  title,
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 20),
          ],
        ),
      ),
    );
  }

  /// 🔥 Drawer Header
  Widget _buildDrawerHeader() {
    return FutureBuilder<Map<String, String>>(
      future: AuthService().getUserInfo(),
      builder: (context, snapshot) {
        final String displayName =
            snapshot.data?['displayName'] ?? 'Guest User';
        final String email = snapshot.data?['email'] ?? 'Not signed in';
        final String photoUrl = snapshot.data?['photoUrl'] ?? '';

        return UserAccountsDrawerHeader(
          accountName: Text(displayName),
          accountEmail: Text(email),
          currentAccountPicture: photoUrl.isNotEmpty
              ? CircleAvatar(
                  backgroundImage: NetworkImage(photoUrl),
                )
              : const CircleAvatar(
                  backgroundColor: Colors.white,
                  child: Icon(Icons.person, size: 40, color: Colors.redAccent),
                ),
          decoration: const BoxDecoration(color: Colors.redAccent),
          otherAccountsPictures: [
            GestureDetector(
              onTap: () async {
                try {
                  print('Signing out...');
                  await AuthService().signOut();
                  print('Sign-out successful');
                  if (context.mounted) {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const SignInScreen()),
                    );
                  }
                } catch (e) {
                  print('Error during sign-out: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error signing out: ${e.toString()}'),
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  }
                }
              },
              child: const CircleAvatar(
                backgroundColor: Colors.white,
                child: Icon(Icons.logout, color: Colors.redAccent),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 🔥 Drawer Item
  Widget _buildDrawerItem(IconData icon, String title, Function onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.redAccent),
      title: Text(title, style: TextStyle(fontSize: 16)),
      onTap: () => onTap(),
    );
  }
}

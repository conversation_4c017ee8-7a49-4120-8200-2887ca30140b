import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:v18ui/Screens/new_home_screen.dart';

class IntroScreen extends StatefulWidget {
  @override
  _IntroScreenState createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  final List<String> sentences = [
    "Hello, how are you",
    "Welcome to V18 , a world free of distraction",
    "But before we start , you need to grant few permissions"
  ];

  int currentSentenceIndex = 0;
  List<String> words = [];
  List<bool> wordVisible = [];
  bool sentenceVisible = true;

  @override
  void initState() {
    super.initState();
    _startSentenceAnimation();
  }

  Future<void> _startSentenceAnimation() async {
    while (currentSentenceIndex < sentences.length) {
      words = sentences[currentSentenceIndex].split(' ');
      wordVisible = List.generate(words.length, (_) => false);
      sentenceVisible = true;
      setState(() {});

      // Show words one by one
      for (int i = 0; i < words.length; i++) {
        await Future.delayed(Duration(milliseconds: 300));
        wordVisible[i] = true;
        setState(() {});
      }

      // Wait before fade out
      await Future.delayed(Duration(seconds: 2));
      sentenceVisible = false;
      setState(() {});

      await Future.delayed(Duration(milliseconds: 500));
      currentSentenceIndex++;
    }

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => NewHomeScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: AnimatedSwitcher(
          duration: Duration(milliseconds: 500),
          child: currentSentenceIndex < sentences.length
              ? sentenceVisible
                  ? Wrap(
                      key: ValueKey(currentSentenceIndex),
                      spacing: 6,
                      alignment: WrapAlignment.center,
                      children: List.generate(words.length, (index) {
                        return AnimatedOpacity(
                          duration: Duration(milliseconds: 300),
                          opacity: wordVisible[index] ? 1.0 : 0.0,
                          child: Text(
                            words[index],
                            style: GoogleFonts.notoSerif(
                                color: Colors.blue.shade200,
                                fontSize: 20,
                                fontWeight: FontWeight.bold),
                          ),
                        );
                      }),
                    )
                  : SizedBox.shrink()
              : Text(
                  "Let's begin!",
                  style: GoogleFonts.orbitron(
                    textStyle: TextStyle(
                      fontSize: 28,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}

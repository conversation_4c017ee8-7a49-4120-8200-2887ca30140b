import 'package:flutter/material.dart';

class NewHomeScreen extends StatefulWidget {
  const NewHomeScreen({super.key});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> {
  final List<DashboardItem> dashboardItems = [
    DashboardItem(
      title: 'Brahmacharya Mode',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      hasToggle: true,
    ),
    DashboardItem(
      title: 'Quit Gambling',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      hasToggle: true,
    ),
    DashboardItem(
      title: 'Control Entertainment Time',
      iconPath: 'assets/icons/brahmacharya_mode.png',
    ),
    DashboardItem(
      title: 'Block Adult Sites',
      iconPath: 'assets/icons/brahmacharya_mode.png',
    ),
    DashboardItem(
      title: 'Control Gaming Time',
      iconPath: 'assets/icons/brahmacharya_mode.png',
    ),
    DashboardItem(
      title: 'Block App',
      iconPath: 'assets/icons/brahmacharya_mode.png',
    ),
    DashboardItem(
      title: 'Focused YouTube',
      iconPath: 'assets/icons/brahmacharya_mode.png',
    ),
  ];

  Widget buildDashboard(List<DashboardItem> items) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];

        return Card(
          margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: ListTile(
            leading: SizedBox(
              width: 48, // standard icon size
              height: 48,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Image.asset(
                  item.iconPath,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            title: Text(item.title),
            trailing: item.hasToggle
                ? Switch(
                    value: false,
                    onChanged: (val) {
                      // TODO: Handle toggle change
                    },
                  )
                : null,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("New Dashboard Layout")),
      body: buildDashboard(dashboardItems),
    );
  }
}

class DashboardItem {
  final String title;
  final String iconPath; // Local asset or network URL
  final bool hasToggle;

  DashboardItem({
    required this.title,
    required this.iconPath,
    this.hasToggle = false,
  });
}

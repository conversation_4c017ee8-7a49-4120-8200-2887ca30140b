import 'package:flutter/material.dart';

class NewHomeScreen extends StatefulWidget {
  const NewHomeScreen({super.key});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> {
  final List<DashboardItem> dashboardItems = [
    DashboardItem(
      title: 'Brahmacharya Mode',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      hasToggle: true,
      gradientColors: [Colors.purple.shade400, Colors.purple.shade600],
    ),
    DashboardItem(
      title: 'Quit Gambling',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      hasToggle: true,
      gradientColors: [Colors.red.shade400, Colors.red.shade600],
    ),
    DashboardItem(
      title: 'Control Entertainment Time',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      gradientColors: [Colors.orange.shade400, Colors.orange.shade600],
    ),
    DashboardItem(
      title: 'Block Adult Sites',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      gradientColors: [Colors.pink.shade400, Colors.pink.shade600],
    ),
    DashboardItem(
      title: 'Control Gaming Time',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      gradientColors: [Colors.green.shade400, Colors.green.shade600],
    ),
    DashboardItem(
      title: 'Block App',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      gradientColors: [Colors.blue.shade400, Colors.blue.shade600],
    ),
    DashboardItem(
      title: 'Focused YouTube',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      gradientColors: [Colors.teal.shade400, Colors.teal.shade600],
    ),
  ];

  Widget buildDashboard(List<DashboardItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: item.gradientColors,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: item.gradientColors.first.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Card(
            margin: EdgeInsets.zero,
            elevation: 0,
            color: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 8,
              ),
              leading: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Image.asset(
                    item.iconPath,
                    fit: BoxFit.contain,
                    color: Colors.white,
                  ),
                ),
              ),
              title: Text(
                item.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              trailing: item.hasToggle
                  ? Switch(
                      value: false,
                      onChanged: (val) {
                        // TODO: Handle toggle change
                      },
                      activeColor: Colors.white,
                      activeTrackColor: Colors.white.withOpacity(0.3),
                      inactiveThumbColor: Colors.white.withOpacity(0.7),
                      inactiveTrackColor: Colors.white.withOpacity(0.2),
                    )
                  : Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white.withOpacity(0.8),
                      size: 18,
                    ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text(
          "Dashboard",
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF667eea),
                Color(0xFF764ba2),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
              Color(0xFFf093fb),
              Color(0xFFf5576c),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: buildDashboard(dashboardItems),
        ),
      ),
    );
  }
}

class DashboardItem {
  final String title;
  final String iconPath; // Local asset or network URL
  final bool hasToggle;
  final List<Color> gradientColors;

  DashboardItem({
    required this.title,
    required this.iconPath,
    this.hasToggle = false,
    this.gradientColors = const [Colors.blue, Colors.blueAccent],
  });
}

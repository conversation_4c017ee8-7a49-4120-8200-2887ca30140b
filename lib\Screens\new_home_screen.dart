import 'package:flutter/material.dart';
import 'package:v18ui/Screens/brahmacharyamode_screen.dart';
import 'package:v18ui/Screens/focused_youtube_screen.dart';
import 'package:v18ui/Screens/safe_app_list.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';

class NewHomeScreen extends StatefulWidget {
  const NewHomeScreen({super.key});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> {
  List<DashboardItem> dashboardItems = [
    DashboardItem(
      title: 'Brahmacharya Mode',
      iconPath: 'assets/icons/brahmacharya_mode.png',
      toggleValue:
          PornDeaddictionService().pornDeaddictionServiceData!.serviceActive!,
      onTap: (context) async {
        if (PornDeaddictionService()
            .pornDeaddictionServiceData!
            .serviceActive!) {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const BrahmacharyaModeScreen()),
          );
        } else {
          await PornDeaddictionService().activateService(context);
        }
      }, //open dialog box from Brahmacharya mode service
    ),
    DashboardItem(
      title: 'Quit Gambling',
      iconPath: 'assets/icons/gambling_icon.png',
      toggleValue: false, // ConfigManager().services!.quitGamblingService,
      onTap: (context) => print('Gambling Mode tapped'),
    ),
    DashboardItem(
      title: 'Control Entertainment Time',
      iconPath: 'assets/icons/entertainment_icon.png',
      onTap: (context) => print("Control Entertainment Time tapped"),
      toggleValue: false,
    ),
    DashboardItem(
      title: 'Block Adult Sites',
      iconPath: 'assets/icons/adult_icon.png',
      onTap: (context) => print("Block Adult Sites tapped"),
      toggleValue: false,
    ),
    DashboardItem(
      title: 'Control Gaming Time',
      iconPath: 'assets/icons/gaming_icon.png',
      onTap: (context) => print("Control Gaming Time tapped"),
      toggleValue: false,
    ),
    DashboardItem(
      title: 'Block App',
      iconPath: 'assets/icons/block_apps_icon.png',
      onTap: (context) => print("Block App tapped"),
      toggleValue: false,
    ),
    DashboardItem(
        title: 'Focused YouTube',
        iconPath: 'assets/icons/focused_youtube.png',
        onTap: (context) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const FocusedYouTubeApp()),
          );
        }),
    DashboardItem(
        title: 'Make your own distraction free zone ',
        iconPath: 'assets/icons/custom_blocking_icon.png',
        onTap: (context) {
          print("Custom blocking clicked");
        }),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("V18"),
        backgroundColor: Colors.lightBlue.shade200,
        centerTitle: true,
      ),
      drawer: Drawer(
          child: ListView(
        padding: EdgeInsets.only(
            top:
                100), // This is a temporary fix , drawer does not look good , modify the UI later
        children: [
          _buildDrawerItem(Icons.check_circle, "Safe App List", () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => SafeAppList()),
            );
          }),
          _buildDrawerItem(Icons.support, "Contact Support", () {}),
          _buildDrawerItem(Icons.star, "Rate Us", () {}),
          _buildDrawerItem(Icons.share, "Refer a Friend", () {}),
          _buildDrawerItem(Icons.card_membership, "Your Subscription", () {}),
        ],
      )),
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: ListView.builder(
          itemCount: dashboardItems.length,
          padding: EdgeInsets.symmetric(vertical: 12),
          itemBuilder: (context, index) {
            final item = dashboardItems[index];
            return DashboardTile(item: item);
          },
        ),
      ),
    );
  }

  /// Drawer Item
  Widget _buildDrawerItem(IconData icon, String title, Function onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title, style: TextStyle(fontSize: 16)),
      onTap: () => onTap(),
    );
  }
}

class DashboardTile extends StatefulWidget {
  DashboardItem item;

  DashboardTile({super.key, required this.item});

  @override
  State<DashboardTile> createState() => _DashboardTileState();
}

class _DashboardTileState extends State<DashboardTile> {
  @override
  Widget build(BuildContext context) {
    return Material(
        child: InkWell(
            onTap: () {
              widget.item.onTap?.call(context);
            },
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 8, horizontal: 15),
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFEEF2F3), Color(0xFFDDEAF5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 12,
                    offset: Offset(0, 6),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        widget.item.iconPath,
                        width: 56,
                        height: 56,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      widget.item.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  if (widget.item.toggleValue != null)
                    Switch(
                      value: widget.item.toggleValue!,
                      onChanged: (val) {
                        if (val) {
                          bool? result = widget.item.onTap?.call(context);
                          if (result == true) {
                            setState(() {
                              widget.item.toggleValue = true;
                            });
                            return;
                          }
                        }
                        ;
                      },
                      activeColor: Colors.blueAccent,
                    )
                ],
              ),
            )));
  }
}

class DashboardItem {
  final String title;
  final String iconPath;
  final String? subtitle;
  Function(BuildContext context)? onTap;
  bool? toggleValue;

  DashboardItem(
      {required this.title,
      required this.iconPath,
      this.subtitle,
      this.onTap,
      this.toggleValue});
}

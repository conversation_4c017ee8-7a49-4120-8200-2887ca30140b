import 'dart:convert';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Services/block_apps_service.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';
import 'package:v18ui/Services/safe_browsing.dart';

class Service {
  BlockAppsService? appBlockService;
  PornDeaddictionService? pornDeaddictionService;
  List<String>? safeAppList;
  SafeBrowsingService? safeBrowsing;

  Service({
    this.appBlockService,
    this.pornDeaddictionService,
    this.safeAppList,
    this.safeBrowsing,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    // Handle the "Services" wrapper
    Map<String, dynamic> servicesData = json['Services'] ?? {};

    return Service(
      appBlockService: servicesData['blockedApps'] != null
          ? BlockAppsService.fromJson(servicesData['blockedApps'])
          : null,
      pornDeaddictionService: servicesData['pornDeaddictionService'] != null
          ? PornDeaddictionService.fromJson(
              servicesData['pornDeaddictionService'])
          : null,
      safeAppList: servicesData['safeAppList'] != null
          ? List<String>.from(servicesData['safeAppList'])
          : [],
      safeBrowsing: servicesData['safeBrowsing'] != null
          ? SafeBrowsingService.fromJson(servicesData['safeBrowsing'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'Services': {
          'blockedApps': appBlockService?.toJson()['blockedApps'] ?? [],
          'pornDeaddictionService': pornDeaddictionService?.toJson(),
          'safeAppList': safeAppList ?? [],
          'safeBrowsing': safeBrowsing?.toJson(),
        }
      };

  Future<bool> updateService(String key, dynamic value) async {
    try {
      String jsonString = await ConfigManager().servicesFile.readAsString();
      Map<String, dynamic> serviceJson = json.decode(jsonString);

      serviceJson['Services'][key] = value;

      // Write back to file
      await ConfigManager()
          .servicesFile
          .writeAsString(json.encode(serviceJson));
      return true;
    } catch (e) {
      print('Error updating service: $e');
      return false;
    }
  }

  Future<bool> deleteService(String key) async {
    try {
      String jsonString = await ConfigManager().servicesFile.readAsString();
      Map<String, dynamic> serviceJson = json.decode(jsonString);

      // Remove the service if it exists
      if (serviceJson['Services'] != null) {
        serviceJson['Services'].remove(key);

        // Write back to file
        await ConfigManager()
            .servicesFile
            .writeAsString(json.encode(serviceJson));
      }
      return true;
    } catch (e) {
      print('Error deleting service: $e');
      return false;
    }
  }
}

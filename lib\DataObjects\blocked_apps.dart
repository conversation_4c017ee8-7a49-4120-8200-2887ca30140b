class BlockedApp {
  final String packageName;
  final String appLabel;
  final String blockingStartTime;
  final String blockingEndTime;

  BlockedApp({
    required this.packageName,
    required this.appLabel,
    required this.blockingStartTime,
    required this.blockingEndTime,
  });

  factory BlockedApp.fromJson(Map<String, dynamic> json) {
    return BlockedApp(
      packageName: json['package_name'],
      appLabel: json['app_label'],
      blockingStartTime: json['blocking_start_time'],
      blockingEndTime: json['blocking_end_time'],
    );
  }

  Map<String, dynamic> toJson() => {
        'package_name': packageName,
        'app_label': appLabel,
        'blocking_start_time': blockingStartTime,
        'blocking_end_time': blockingEndTime,
      };
}
